package com.botong.quartz.task;

import com.botong.api.module.system.ElectricityBillApi;
import com.botong.framework.common.utils.Result;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 越秀电费账单定时更新任务
 * 定时调用越秀电费账单更新接口，每月6号、16号、26号执行
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
@Slf4j
@Service
@AllArgsConstructor
public class ElectricityBillUpdateTask {

    private final ElectricityBillApi electricityBillApi;

    /**
     * 定时更新越秀电费账单数据
     * 执行时间：每月6号、16号、26号的凌晨2点执行
     * Cron表达式：0 0 2 6,16,26 * ?
     *
     * @param params 任务参数（可选）
     */
    public void run(String params) {
        log.info("开始执行越秀电费账单定时更新任务，参数：{}", params);

        try {
            // 调用越秀电费账单更新接口
            Result<String> result = electricityBillApi.updateYxBill();

            if (result != null && result.isSuccess()) {
                log.info("越秀电费账单更新任务执行成功，结果：{}", result.getData());
            } else {
                String errorMsg = result != null ? result.getMsg() : "接口返回结果为空";
                log.error("越秀电费账单更新任务执行失败，错误信息：{}", errorMsg);
                throw new RuntimeException("越秀电费账单更新任务执行失败: " + errorMsg);
            }

        } catch (Exception e) {
            log.error("越秀电费账单更新任务执行失败", e);
            throw new RuntimeException("越秀电费账单更新任务执行失败: " + e.getMessage(), e);
        }

        log.info("越秀电费账单定时更新任务执行完成");
    }
}
